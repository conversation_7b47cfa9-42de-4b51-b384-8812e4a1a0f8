<template>
  <div class="flex gap-4">
    <div>
      <PwTabs
        :tabs="translatedTabsFilterProduct"
        :defaultActiveIndex="0"
        @tab-click="handleTabClickFilterTypes"
        class="flex"
        :class="{ dark: isDark }"
      />
    </div>
    <PwTabs
      :tabs="translatedTabsFilterStatus"
      :defaultActiveIndex="1"
      class="flex"
      @tab-click="handleTabClickFilterStatus"
      :class="{ dark: isDark }"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, type Ref, ref } from 'vue'
import type { ITab } from '@/components/filter/tabs/interfaces/tabs.interface'
import { tabsFilterProductFields, tabsFilterStatusFields } from '@/utils/tabs'
import {
  PRODUCTMANAGEMENT,
  SET_FILTER_BY_STATUS,
  SET_FILTER_BY_TYPE
} from '@/store/modules/productmanagement/constants/store.constants'

export default defineComponent({
  name: 'PwTabs'
})
</script>

<script setup lang="ts">
import { PwTabs } from 'parlem-webcomponents-common'
import { useStore } from 'vuex'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const isDark = ref(localStorage.theme === 'dark')
const store = useStore()
const tabsFilterProduct: Ref<ITab[]> = ref([...tabsFilterProductFields])
const tabsFilterStatus: Ref<ITab[]> = ref(tabsFilterStatusFields)

const translatedTabsFilterProduct = computed(() => {
  return tabsFilterProduct.value.map((tab) => ({
    ...tab,
    label: t(tab.label)
  }))
})

const translatedTabsFilterStatus = computed(() => {
  return tabsFilterStatus.value.map((tab) => ({
    ...tab,
    label: t(tab.label)
  }))
})

const handleTabClickFilterTypes = (tab: ITab) => {
  store.dispatch(`${PRODUCTMANAGEMENT}/${SET_FILTER_BY_TYPE}`, tab.value)
}
const handleTabClickFilterStatus = (tab: ITab) => {
  store.dispatch(`${PRODUCTMANAGEMENT}/${SET_FILTER_BY_STATUS}`, tab.value)
}
</script>
