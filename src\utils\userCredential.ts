import store from '@/store/index'
import { checkUserCredentialsMsal } from 'parlem-webcomponents-common'
import type { IAccount } from '@/store/modules/productmanagement/interfaces/account.interface'
import {
  PRODUCTMANAGEMENT,
  UPDATE_ACCESS_TOKEN,
  UPDATE_ACCOUNT,
  SET_AUTHENTICATION_ERROR,
  GET_MSAL_INSTANCE,
  SET_MSAL_INSTANCE
} from '@/store/modules/productmanagement/constants/store.constants'
import type { IPublicClientApplication } from '@azure/msal-browser'

// Funció per obtenir el token

export default async function checkUserCredentials() {
  store.dispatch(`${PRODUCTMANAGEMENT}/${SET_AUTHENTICATION_ERROR}`, false)

  try {
    let msalInstance: IPublicClientApplication | undefined = undefined
    msalInstance = (window as any).msalInstance
      ? (window as any).msalInstance
      : store.getters[`${PRODUCTMANAGEMENT}/${GET_MSAL_INSTANCE}`]

    const msalResponse = await checkUserCredentialsMsal(msalInstance)
    const accessTokenResponse = msalResponse.result
    store.dispatch(`${PRODUCTMANAGEMENT}/${SET_MSAL_INSTANCE}`, msalResponse.msalInstance)

    if (accessTokenResponse && accessTokenResponse.idToken) {
      const accessToken: string = accessTokenResponse.idToken
      const account: IAccount = accessTokenResponse.account

      store.dispatch(`${PRODUCTMANAGEMENT}/${UPDATE_ACCESS_TOKEN}`, accessToken)
      store.dispatch(`${PRODUCTMANAGEMENT}/${UPDATE_ACCOUNT}`, account)
    } else {
      store.dispatch(`${PRODUCTMANAGEMENT}/${SET_AUTHENTICATION_ERROR}`, true)
    }
  } catch (error) {
    console.error(`Error during authentication: ${error}`)
    store.dispatch(`${PRODUCTMANAGEMENT}/${SET_AUTHENTICATION_ERROR}`, true)
  }
}

/* export default async function checkUserCredentials() {
  store.dispatch(`${PRODUCTMANAGEMENT}/${SET_AUTHENTICATION_ERROR}`, false)

  try {
    const msalConfig = store.getters[`${PRODUCTMANAGEMENT}/${GET_MSAL_CONFIG}`]
    const accessTokenResponse = await checkUserCredentialsMsal(msalConfig)

    if (accessTokenResponse && accessTokenResponse.idToken) {
      const accessToken: string = accessTokenResponse.idToken
      const account: IAccount = accessTokenResponse.account

      store.dispatch(`${PRODUCTMANAGEMENT}/${SET_ACCESS_TOKEN}`, accessToken)
      store.dispatch(`${PRODUCTMANAGEMENT}/${SET_ACCOUNT}`, account)
    } else {
      store.dispatch(`${SELLMANAGEMENT}/${SET_AUTHENTICATION_ERROR}`, true)
    }
  } catch (error) {
    console.error(`Error during authentication: ${error}`)
    store.dispatch(`${SELLMANAGEMENT}/${SET_AUTHENTICATION_ERROR}`, true)
  }
}
 */
