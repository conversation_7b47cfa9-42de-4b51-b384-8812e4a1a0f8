import axios from 'axios'
import {
  X_PARLEM_API_KEY,
  CONTENT_TYPE,
  APPLICATION_JSON_PATCH_JSON,
  API,
  LANGUAGES,
  PICKLISTS,
  PROVISIONING,
  PICKLIST,
  COMPANIES,
  CUSTOMERS,
  PROVIDERS,
  S<PERSON><PERSON><PERSON>IPTIONS,
  SERVICES,
  ROAMING,
  PROCESSES,
  SYNCRONIZATIONS,
  CUSTOMER
} from '../../constants/services.constants'
import type { IApiRes, IHeaders } from '../interfaces'
import type { IApiProvisioning } from './interfaces/api-provisioning.interface'
import type { IPickList } from './interfaces/picklist.interface'
import type { IUpdateRoamingStatus } from './interfaces/update-roaming-status.interface'
import type { IRoamingParams } from './interfaces/roaming-status-api-params.interface'

const provisioningHeaders: IHeaders = {
  headers: {
    [X_PARLEM_API_KEY]: import.meta.env.VITE_API_KEY_PROVISIONING,
    [CONTENT_TYPE]: APPLICATION_JSON_PATCH_JSON
  }
}

const apiProvisioningService: IApiProvisioning = {
  async getPicklists(language: string) {
    const url: string = `${
      import.meta.env.VITE_BASE_URL
    }/${PROVISIONING}/${API}/${PICKLIST}/${LANGUAGES}/${language}/${PICKLISTS}`
    try {
      const response: IApiRes<IPickList[]> = await axios.get(url, provisioningHeaders as IHeaders)
      return response.data
    } catch (error: any) {
      return error.response
    }
  },

  async getInitRoamingStatus(payload: IRoamingParams) {
    const url: string = `${import.meta.env.VITE_BASE_URL}/${PROVISIONING}/${API}/${COMPANIES}/${
      payload.company
    }/${CUSTOMERS}/${payload.customerId}/${PROVIDERS}/${payload.provider}/${SUBSCRIPTIONS}/${
      payload.subscriptionId
    }/${SERVICES}/${ROAMING}`
    try {
      const response: IApiRes<IUpdateRoamingStatus> = await axios.get(
        url,
        provisioningHeaders as IHeaders
      )
      return response.data
    } catch (error: any) {
      return error.response
    }
  },

  async updateRoamingStatus(payload: IRoamingParams) {
    const url: string = `${import.meta.env.VITE_BASE_URL}/${PROVISIONING}/${API}/${COMPANIES}/${
      payload.company
    }/${CUSTOMERS}/${payload.customerId}/${PROVIDERS}/${payload.provider}/${SUBSCRIPTIONS}/${
      payload.subscriptionId
    }/${SERVICES}/${ROAMING}`
    try {
      const response: IApiRes<IUpdateRoamingStatus> = await axios.put(
        url,
        payload.status,
        provisioningHeaders as IHeaders
      )
      return response
    } catch (error: any) {
      return error.response
    }
  },
  async syncCustomerFromGossan(query: {
    documentNumber: string
    user: string
    persistCustomerSyncChanges: boolean
  }): Promise<IApiRes<void>> {
    const url: string = `${
      import.meta.env.VITE_BASE_URL
    }/${PROVISIONING}/${API}/${PROCESSES}/${SYNCRONIZATIONS}/${CUSTOMER}`

    try {
      const response: IApiRes<void> = await axios.post(url, null, {
        ...provisioningHeaders, 
        params: query 
      })
      return response
    } catch (error: any) {
      return error.response
    }
  }
}

export default apiProvisioningService
