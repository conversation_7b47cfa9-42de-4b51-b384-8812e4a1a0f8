import type { IState } from '@/store/modules/productmanagement/interfaces/state.interface'
import type { IUserInfo } from '@/services/api/shoppingcart/interfaces/user-info.interface'
import type { IAccount } from '@/store/modules/productmanagement/interfaces/account.interface'
import type {
  IAllContractProducts,
  IContractProduct
} from '@/services/api/crm/interfaces/contract-products.interface'
import type { IPickList } from '@/services/api/provisioning/interfaces/picklist.interface'
import type { IUpdateRoamingStatus } from '@/services/api/provisioning/interfaces/update-roaming-status.interface'
import type { IPublicClientApplication } from '@azure/msal-browser'

export const setMsalInstance = (state: IState, msalInstance: IPublicClientApplication): void => {
  state.msalInstance = msalInstance
}

export const updateAccessToken = (state: IState, accessToken: string): void => {
  state.accessToken = accessToken
}

export const updateAccount = (state: IState, account: IAccount): void => {
  state.account = account
}

export const setLang = (state: IState, lang: string): void => {
  state.lang = lang
}

export const setUsername = (state: IState, username: string): void => {
  state.username = username
}

export const setCompany = (state: IState, company: string): void => {
  state.company = company
}

export const setUserCompany = (state: IState, userCompany: string): void => {
  state.userCompany = userCompany
}

export const setUserInfo = (state: IState, userInfo: IUserInfo): void => {
  state.userInfo = userInfo
}

export const setAuthenticationError = (state: IState, error: boolean): void => {
  state.authenticationError = error
}

export const setDocumentNumber = (state: IState, documentNumber: string): void => {
  state.documentNumber = documentNumber
}

export const getContractProducts = (
  state: IState,
  contractProducts: IAllContractProducts
): void => {
  state.contractProducts = contractProducts.items
  state.totalProducts = contractProducts.total
  if (!state.customerData && contractProducts.items?.length > 0) {
    const firstContractProduct = contractProducts.items[0]
    state.customerData = {
      customerId: firstContractProduct.customerId,
      completeName: firstContractProduct.customerName,
      email: firstContractProduct.contactEmail,
      phone: firstContractProduct.contactPhone,
      documentNumber: firstContractProduct.documentNumber,
      documentType: firstContractProduct.documentType
    }
  }
}

export const getPicklists = (state: IState, picklists: IPickList[]): void => {
  state.picklists = picklists
}

export const getPicklistsCRM = (state: IState, picklists: IPickList[]): void => {
  state.picklistsCRM = picklists
}

export const setIsLoading = (state: IState, isLoading: boolean): void => {
  state.isLoading = isLoading
}

export const setFilterByType = (state: IState, provisioningClass: string): void => {
  state.filterTypeByProduct = provisioningClass
}

export const setFilterByStatus = (state: IState, statusProduct: string): void => {
  state.filterByStatus = statusProduct
  state.initSearchParams.state = statusProduct
}


export const loadMoreContractProducts = (
  state: IState,
  contractProducts: IContractProduct[]
): void => {
  state.contractProducts?.push(...contractProducts)
}

// export const setRoamingStatus = (state: IState, roamingStatus: IUpdateRoamingStatus): void => {
//   state.roamingStatus = roamingStatus
// }

export const updateRoamingStatus = (state: IState, roamingStatus: IUpdateRoamingStatus): void => {
  state.roamingStatus = roamingStatus
}

export const setShowRoamingPopup = (state: IState, value: any): void => {
  state.showRoamingPopup = value
}

export const setProductToToggleRoaming = (state: IState, product: any): void => {
  state.productToToggleRoaming = product
}

export const setCustomerSyncStatus = (state: IState, status: number): void => {
  state.customerSyncStatus = status
}
