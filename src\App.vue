<template>
  <div class="p-4">
    <pw-product-management
      :lang="lang"
      :company="company"
      :document-number="documentNumber"
      :hide-personal-data="hidePersonalData"
    ></pw-product-management>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive } from 'vue'
import { defineUserTheme } from 'parlem-webcomponents-common'

const lang = ref('ca')
const company = ref('Parlem')

const userInfo = reactive({
  id: 1149,
  username: 'cfont',
  providerCode: '0',
  isOnline: false,
  company: ['Parlem'],
  tags: ['Next', 'IRU', 'COMP'],
  channel: 'Call Center'
})

const documentNumber = computed(() => {
  const urlParams = new URLSearchParams(window.location.search)
  return urlParams.get('doc')
})

const hidePersonalData = computed(() => {
  const urlParams = new URLSearchParams(window.location.search)
  return urlParams.get('hidePersonalData')
})

onMounted(() => {
  defineUserTheme()
})
</script>

<style scoped></style>
