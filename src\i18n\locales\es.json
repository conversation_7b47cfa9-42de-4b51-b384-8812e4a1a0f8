{"states": {"active": "Activo", "activating": "Activando", "confirmed": "Provisionando", "petitioned": "Provisionando", "pending": "Pendiente", "canceling": "Cancelando", "canceled": "Cancelado", "incidence": "En incidencia", "incidented": "En incidencia", "suspending": "Suspensión", "deactivating": "Desactivación", "suspended": "Suspendido", "migrated": "Migrado", "closing": "Cierre", "closed": "<PERSON><PERSON><PERSON>", "unconfirmed": "<PERSON>ar"}, "roaming": {"activate": "activar", "deactivate": "desactivar", "activate-roaming": "Activar el roaming", "deactivate-roaming": "Desactivar el roaming", "activating": "Activando roaming...", "confirmationQuestion": "¿Estás seguro que quieres", "confirmationEnd": "el roaming de este producto?", "activatedSuccess": "¡El roaming se ha activado! :)", "deactivatedSuccess": "¡El roaming se ha desactivado! :)", "error": "¡Ha habido un error!", "unexpectedError": "<PERSON><PERSON>r inesperado"}, "products": {"products": "productos", "actions": "Acciones de producto", "sync": "Sincronizar", "unsubscribe": "Dar <PERSON> b<PERSON>", "noProductsAvailable": "No hay productos disponibles", "checkDocumentNumber": "Revisa que el número de documento del cliente sea correcto", "selectProduct": "Selecciona un producto.", "unsubscribeSuccess": "¡El producto se ha dado de baja correctamente! :)", "unsubscribeError": "¡Ha habido un error al dar de baja el producto!", "unsubscribeMultipleSuccess": "¡Los productos se han dado de baja correctamente! :)", "unsubscribeMultipleError": "¡Ha habido un error al dar de baja los productos!", "confirmUnsubscribe": "¿Seguro que quieres dar de baja este producto", "unsubscribeReason": "¿Cuál es el motivo de la baja?", "unsubscribeReasonPlaceholder": "Escribe aquí el motivo de la baja...", "selectUnsubscribeDate": "Selecciona la fecha de la baja", "selectDate": "Selecciona una fecha", "processingUnsubscribe": "Procesando la baja...", "setupboxAddress": "Indica por favor la dirección de recogida del setupbox:", "cancel": "No, cancelar", "confirmUnsubscribeButton": "Sí, dar de baja", "unsubscribeReasonRequired": "¿Cuál es el motivo de la baja?", "title": "PRODUCTOS", "contractedProducts": "PRODUCTOS CONTRATADOS"}, "customer": {"documentNumber": "Número documento cliente", "documentNumberPlaceholder": "Escribe aquí el número de dni, nie, cif o pasaporte del cliente...", "searchButton": "Buscar cliente", "title": "CLIENTE", "idNumber": "Nº Id Cliente", "contactEmail": "<PERSON><PERSON>", "contactPhone": "Teléfono de contacto", "sync": {"success": "¡El cliente se ha sincronizado correctamente! :)", "error": "¡Ha habido un error al sincronizar el cliente!"}, "unsubscribe": {"title": "¿Seguro que quieres dar de baja este cliente?", "message": "<PERSON> das de baja este cliente, dar<PERSON> de baja todos sus productos activos.", "reason": "¿Cuál es el motivo de la baja?", "reasonPlaceholder": "Escribe aquí el motivo de la baja...", "success": "¡El cliente se ha dado de baja correctamente! :)", "error": "¡Ha habido un error al dar de baja el cliente!"}}, "pagination": {"listContains": "La lista contiene", "element": "elemento", "elements": "elementos", "of": "de", "showMoreItems": "Mostrar más elementos"}, "common": {"cancel": "No, cancelar", "confirm": "Sí, dar de baja"}, "tabs": {"product": {"all": "Todos", "mobile": "Móvil", "fiber": "Fibra", "landline": "<PERSON><PERSON>", "unsubscribe": "Dar de baja los productos seleccionados"}, "status": {"all": "Todos", "active": "Activos", "pending": "Pendientes", "inactive": "Inactivos"}, "customer": {"sync": "Sincronizar cliente", "delete": "Eliminar cliente"}}, "columns": {"contractProductId": "ID Producto Contratado", "productName": "Nombre producto", "type": "Tipo", "msisdn": "Msisdn", "terminal": "Terminal", "state": "Estado", "activationDate": "Fecha de activación", "soldAt": "<PERSON><PERSON>", "effectiveEndDate": "<PERSON><PERSON>", "unsubscribeReason": "Motivo baja", "actions": "Acciones", "customerId": "ID Cliente", "provider": "<PERSON><PERSON><PERSON><PERSON>", "externalSubscriptionId": "ID Suscripción Externa", "contractedSubscriptionId": "ID Suscripción Contratada", "provisioningClass": "Clase de Aprovisionamiento", "provisioningSubClass": "Subclase de Aprovisionamiento"}}