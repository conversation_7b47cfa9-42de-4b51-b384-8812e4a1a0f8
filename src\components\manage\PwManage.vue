<template>
  <PwTabs
    :class="{ dark: isDark }"
    :tabs="translatedTabActions"
    :defaultActiveIndex="'All'"
    @tab-click="handleTabClickProductActions"
    class="flex"
  />
  <PwPopup
    v-if="showPopupConfirmation"
    :close-button="true"
    :cancel-button="isHasTV ? false : true"
    :cancel-button-text="$t('products.cancel')"
    :accept-button="isHasTV ? false : true"
    :accept-button-text="$t('products.confirmUnsubscribeButton')"
    :accept-button-disabled="isLoadingUnsubscribe || !motiveUnsubscribe || !unsubscribeDate"
    @close="closeModalConfirmation"
    @accept="unsubscribeProduct"
    @cancel="closeModalConfirmation"
    :card-classes="'!min-h-[350px] !min-w-[75%] p-6 bg-white dark:bg-dark-gray'"
  >
    <div class="mb-3 mt-6">
      <div class="mx-auto flex items-center justify-center h-24 w- rounded-full">
        <font-awesome-icon icon="fa fa-triangle-exclamation" class="text-primary h-24 w-24" />
      </div>
      <h3 class="font-bold text-center text-2xl text-black dark:text-white my-8">
        {{ getConfirmationMessage() }}
      </h3>

      <PwSelectAutocomplete
        :required="true"
        item-title="label"
        item-value="value"
        :label="$t('products.unsubscribeReason')"
        :items="listUnsubscribeReason"
        :placeholder="$t('products.unsubscribeReasonPlaceholder')"
        class="w-full"
        :customInputClass="`!bg-gray`"
        @update:model-value="handleUpdateUnsubscribeReason"
      ></PwSelectAutocomplete>

      <PwInputText
        v-model="unsubscribeDate"
        class="w-full mt-6"
        type="date"
        :label="$t('products.selectUnsubscribeDate')"
        :placeholder="$t('products.selectDate')"
        :customLabelClass="'!mb-0 '"
        :customInputClass="`!p-2 !pl-3 !h-[42px] !bg-gray`"
      />

      <div v-if="isLoadingUnsubscribe" class="flex justify-center mt-4">
        <font-awesome-icon icon="fa fa-spinner" class="animate-spin text-primary h-6 w-6" />
        <span class="ml-2 text-primary">{{ $t('products.processingUnsubscribe') }}</span>
      </div>

      <div v-if="isHasTV">
        <p class="text-sm font-bold -mb-1 mt-4">
          {{ $t('products.setupboxAddress') }}
        </p>
        <PwStreetMap
          from="removeAgile"
          :reset-button-text="$t('products.cancel')"
          :submit-button-text="$t('products.confirmUnsubscribeButton')"
          @save-address="unsubscribeProduct"
          @reset-address="closeModalConfirmation"
        ></PwStreetMap>
        <p v-if="isSetUnsubscribeReason" class="text-sm font-bold text-error">
          {{ $t('products.unsubscribeReasonRequired') }}
        </p>
      </div>
    </div>
  </PwPopup>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
export default defineComponent({
  name: 'PwManage'
})
</script>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'
import type { Ref, PropType, ComputedRef } from 'vue'
import { useI18n } from 'vue-i18n'
import { PwTabs, PwPopup, PwSelectAutocomplete, PwInputText } from 'parlem-webcomponents-common'
import PwStreetMap from '../street-map/PwStreetMap.vue'
import type { ITab } from '@/components/filter/tabs/interfaces/tabs.interface'
import { productActionsTabs } from '@/utils/tabs'
import type { IContractProduct } from '@/services/api/crm/interfaces/contract-products.interface'
import { toastUtils } from '@/utils/toast'
import {
  PRODUCTMANAGEMENT,
  DEACTIVATE_CONTRACT_PRODUCT_BY_ID,
  GET_PICKLISTS_CRM
} from '@/store/modules/productmanagement/constants/store.constants'
import { useStore } from 'vuex'

const { t } = useI18n()
const props = defineProps({
  updateSelectedProductForUnsubscribe: {
    type: Array as PropType<IContractProduct[]>,
    default: () => []
  }
})
const isDark = ref(localStorage.theme === 'dark')

const emit = defineEmits<{
  refreshTable: []
}>()

const store = useStore()
const isVisibleActionsDetails = ref(false)
const showPopupConfirmation = ref(false)
const motiveUnsubscribe: Ref<any> = ref()
const unsubscribeDate: Ref<any> = ref()
const isSetUnsubscribeReason = ref(false)
const isHasTV: Ref<boolean> = ref(false)
const initTabActions: Ref<ITab[]> = ref([...productActionsTabs])
const tabActions: Ref<ITab[]> = ref([])
const isLoadingUnsubscribe = ref(false)

const picklistsCRM: ComputedRef<any> = computed(
  () => store.getters[`${PRODUCTMANAGEMENT}/${GET_PICKLISTS_CRM}`]
)

watch(
  () => props.updateSelectedProductForUnsubscribe,
  (selectedProductForUnsubscribe) => {
    isHasTV.value = selectedProductForUnsubscribe.some(
      (productForUnsubscribe) => productForUnsubscribe.provisioningSubClass === 'TVSetupBox'
    )
  },
  { immediate: true, deep: true }
)

onMounted(() => {
  setTabActions()
})

const setTabActions = () => {
  tabActions.value = initTabActions.value.map((tab: ITab) => {
    const newTab = { ...tab }
    newTab.disabled = props.updateSelectedProductForUnsubscribe?.length ? false : true
    return newTab
  })
}

const translatedTabActions = computed(() => {
  return tabActions.value.map(tab => ({
    ...tab,
    label: t(tab.label)
  }))
})

const listUnsubscribeReason = computed(() => {
  const closeReason = picklistsCRM.value.find((item: any) => item.name === 'CloseReason')
  return closeReason ? closeReason.values : []
})

const getTerminalValue = (product: any) => {
  if (product.provisioningClass === 'TV') {
    return product.terminalSerialNumber || '-'
  }

  return product.number || product.msisdn || '-'
}

const getConfirmationMessage = () => {
  const selectedProducts = props.updateSelectedProductForUnsubscribe

  if (selectedProducts.length === 1) {
    const terminalValue = getTerminalValue(selectedProducts[0])
    return `${t('products.confirmUnsubscribe')} ${terminalValue}?`
  } else if (selectedProducts.length > 1) {
    return `${t('products.confirmUnsubscribe')} ${selectedProducts.length} ${t('products.products')}?`
  }

  return t('products.confirmUnsubscribe')
}

const handleTabClickProductActions = (tab: ITab) => {
  if (['delete-products'].includes(tab.value as string)) {
    showPopupConfirmation.value = true
  }
}

/* const toggleShowActions = () => {
  isVisibleActionsDetails.value = !isVisibleActionsDetails.value
} */

const handleUpdateUnsubscribeReason = (unsubscribeReasonFromSelectBadges: string) => {
  motiveUnsubscribe.value = unsubscribeReasonFromSelectBadges
}

const closeModalConfirmation = () => {
  isVisibleActionsDetails.value = false
  showPopupConfirmation.value = false
}

const unsubscribeProduct = async (address: any) => {
  if (motiveUnsubscribe.value === undefined) {
    isSetUnsubscribeReason.value = true
    return
  }
  isSetUnsubscribeReason.value = false

  const productsForUnsubscribe: IContractProduct[] = props.updateSelectedProductForUnsubscribe

  if (!productsForUnsubscribe.length) {
    toastUtils.showToast('error', t('products.selectProduct'))
    return
  }

  isLoadingUnsubscribe.value = true

  if (productsForUnsubscribe.length > 1) {
    await unsubscribeMultipleProducts(productsForUnsubscribe, address)
  } else {
    await unsubscribeSingleProduct(productsForUnsubscribe[0], address)
  }

  isLoadingUnsubscribe.value = false

  isVisibleActionsDetails.value = false
  emit('refreshTable')
  showPopupConfirmation.value = false
  motiveUnsubscribe.value = ''
}

const unsubscribeSingleProduct = async (product: IContractProduct, address: any) => {
  const payload = {
    id: product.id,
    deactivateReason: motiveUnsubscribe.value.value,
    effectiveEndDate: new Date(unsubscribeDate.value).toISOString(),
    logisticAddress: isHasTV.value ? address : {}
  }

  const status: number = await store.dispatch(
    `${PRODUCTMANAGEMENT}/${DEACTIVATE_CONTRACT_PRODUCT_BY_ID}`,
    payload
  )

  if (status === 200) {
    toastUtils.showToast('success', t('products.unsubscribeSuccess'))
  } else {
    toastUtils.showToast('error', t('products.unsubscribeError'))
  }
}

const unsubscribeMultipleProducts = async (products: IContractProduct[], address: any) => {
  const productIds = products.map((product) => product.id)

  const payload = {
    contractProductsId: productIds,
    deactivateReason: motiveUnsubscribe.value.value,
    logisticAddress: isHasTV.value ? address : {}
  }

  const status: number = await store.dispatch(
    `${PRODUCTMANAGEMENT}/deactivateContractProductList`,
    payload
  )

  if (status === 200) {
    toastUtils.showToast('success', t('products.unsubscribeMultipleSuccess'))
  } else {
    toastUtils.showToast('error', t('products.unsubscribeMultipleError'))
  }
}

watch(
  () => props.updateSelectedProductForUnsubscribe,
  () => {
    setTabActions()
  },
  { deep: true }
)
</script>
