{"states": {"active": "Active", "activating": "Activating", "confirmed": "Provisioning", "petitioned": "Provisioning", "pending": "Pending", "canceling": "Canceling", "canceled": "Canceled", "incidence": "In incidence", "incidented": "In incidence", "suspending": "Suspension", "deactivating": "Deactivation", "suspended": "Suspended", "migrated": "Migrated", "closing": "Closing", "closed": "Closed", "unconfirmed": "Unconfirmed"}, "roaming": {"activate": "activate", "deactivate": "deactivate", "activate-roaming": "Activate roaming", "deactivate-roaming": "Deactivate roaming", "activating": "Activating roaming...", "confirmationQuestion": "Are you sure you want to", "confirmationEnd": "roaming for this product?", "activatedSuccess": "Roaming has been activated! :)", "deactivatedSuccess": "Roaming has been deactivated! :)", "error": "There was an error!", "unexpectedError": "Unexpected error"}, "products": {"products": "products", "actions": "Product actions", "sync": "Synchronize", "unsubscribe": "Unsubscribe", "noProductsAvailable": "No products available", "checkDocumentNumber": "Check that the customer's document number is correct", "selectProduct": "Select a product.", "unsubscribeSuccess": "The product has been successfully unsubscribed! :)", "unsubscribeError": "There was an error unsubscribing the product!", "unsubscribeMultipleSuccess": "The products have been successfully unsubscribed! :)", "unsubscribeMultipleError": "There was an error unsubscribing the products!", "confirmUnsubscribe": "Are you sure you want to unsubscribe this product", "unsubscribeReason": "What is the reason for unsubscribing?", "unsubscribeReasonPlaceholder": "Write here the reason for unsubscribing...", "selectUnsubscribeDate": "Select the unsubscribe date", "selectDate": "Select a date", "processingUnsubscribe": "Processing unsubscription...", "setupboxAddress": "Please indicate the pickup address for the setupbox:", "cancel": "No, cancel", "confirmUnsubscribeButton": "Yes, unsubscribe", "unsubscribeReasonRequired": "What is the reason for unsubscribing?", "title": "PRODUCTS", "contractedProducts": "CONTRACTED PRODUCTS"}, "customer": {"documentNumber": "Customer document number", "documentNumberPlaceholder": "Write here the customer's DNI, NIE, CIF or passport number...", "searchButton": "Search customer", "title": "CLIENT", "idNumber": "Client ID No.", "contactEmail": "Contact email", "contactPhone": "Contact phone", "sync": {"success": "The client has been synchronized successfully! :)", "error": "There was an error synchronizing the client!"}, "unsubscribe": {"title": "Are you sure you want to unsubscribe this client?", "message": "If you unsubscribe this client, all their active products will be unsubscribed.", "reason": "What is the reason for unsubscribing?", "reasonPlaceholder": "Write the reason for unsubscribing here...", "success": "The client has been unsubscribed successfully! :)", "error": "There was an error unsubscribing the client!"}}, "pagination": {"listContains": "The list contains", "element": "item", "elements": "items", "of": "of", "showMoreItems": "Show more items"}, "common": {"cancel": "No, cancel", "confirm": "Yes, unsubscribe"}, "tabs": {"product": {"all": "All", "mobile": "Mobile", "fiber": "Fiber", "landline": "Landline", "unsubscribe": "Unsubscribe selected products"}, "status": {"all": "All", "active": "Active", "pending": "Pending", "inactive": "Inactive"}, "customer": {"sync": "Synchronize customer", "delete": "Delete customer"}}, "columns": {"contractProductId": "Contract Product ID", "productName": "Product Name", "type": "Type", "msisdn": "Msisdn", "terminal": "Terminal", "state": "State", "activationDate": "Activation Date", "soldAt": "Sold At", "effectiveEndDate": "End Date", "unsubscribeReason": "Unsubscribe Reason", "actions": "Actions", "customerId": "Customer ID", "provider": "Provider", "externalSubscriptionId": "External Subscription ID", "contractedSubscriptionId": "Contracted Subscription ID", "provisioningClass": "Provisioning Class", "provisioningSubClass": "Provisioning Subclass"}}