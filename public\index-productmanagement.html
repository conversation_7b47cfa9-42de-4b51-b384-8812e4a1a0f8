<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Product management</title>
    <script src="#{cdn_base_url}#/productmanagement/parlem-webcomponents-productmanagement.umd.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/ag-grid-community/dist/ag-grid-community.min.js"></script>
  </head>
  <body>
    <div id="pw-product-management"></div>
  </body>
  <script>
    // Get params from url Add to url https://example.com/en/sells
    const queryString = window.location.search
    const urlParams = new URLSearchParams(queryString)
    const lang = urlParams.get("lang") || "ca";
    const userInfo = urlParams.get("userInfo");
    const company = urlParams.get("company") || "Parlem";
    const documentNumber = urlParams.get("doc");
    const hidePersonalData = urlParams.get("hidePersonalData");
    // Load webcomponent with dynamic params
    let productmanagement = document.createElement('pw-product-management')
    const config = {
      lang: lang ? lang : 'ca', 
      company: company ? company : 'Parlem', 
      userInfo: userInfo ? userInfo : '',
      documentNumber: documentNumber ? documentNumber : '',
      hidePersonalData: hidePersonalData ? hidePersonalData : ''     
    }
    const configStr = JSON.stringify(config)
    productmanagement.setAttribute('config', configStr)
    const pwProductManagement = document.getElementById('pw-product-management')
    pwProductManagement.appendChild(productmanagement)

    </script>
</html>
