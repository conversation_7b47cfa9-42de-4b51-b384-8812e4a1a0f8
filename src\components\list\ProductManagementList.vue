<template>
  <ag-grid-vue
    v-if="rowData?.length"
    :class="style"
    :columnDefs="columnDefs"
    :rowData="rowData"
    :autoSizeStrategy="autoSizeStrategy"
    :suppressCellSelection="false"
    :defaultColDef="defaultColDef"
    :rowSelection="rowSelection"
    :isFullWidthRow="isFullWidthRow"
    :fullWidthCellRenderer="fullWidthCellRenderer"
    :getRowHeight="getRowHeight"
    :enableCellTextSelection="true"
    :rowClassRules="rowClassRules"
    @grid-ready="onGridReady"
    :style="{
      '--ag-header-background-color': props.itemsByProps
        ? isLightTheme
          ? '#f1f1f1'
          : '#2D3033'
        : isLightTheme
        ? '#e4e4e4'
        : '#3c4043'
    }"
    @rowSelected="getSelectedRows"
  />
  <div v-else class="flex flex-col w-full h-full justify-center items-center">
    <font-awesome-icon icon="fa-solid fa-list-check" class="w-32 text-gray mb-4" />
    <div class="text-dark-gray text-lg font-medium">{{ $t('products.noProductsAvailable') }}</div>
    <div v-if="!customerData" class="text-dark-gray-light">
      {{ $t('products.checkDocumentNumber') }}
    </div>
  </div>

  <PwPopup
    :class="{ dark: isDark }"
    v-if="popupRoamingVisible"
    @close="closePopup"
    @cancel="closePopup"
    @accept="confirmRoaming"
  />
</template>

<script lang="ts">
import { defineComponent } from 'vue'
export default defineComponent({
  name: 'ProductManagementList'
})
</script>

<script setup lang="ts">
import { ref, type Ref, onBeforeMount, computed, type ComputedRef, watch } from 'vue'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'
import { AgGridVue } from 'ag-grid-vue3'
import type { RowSelectedEvent } from 'ag-grid-community'
import type { IContractProduct } from '@/services/api/crm/interfaces/contract-products.interface'
import type { ICustomer } from '@/store/modules/productmanagement/interfaces/customer.interface'
import columnsFieldOptions from '@/utils/columns'
import {
  PRODUCTMANAGEMENT,
  GET_CONTRACT_PRODUCTS,
  GET_CUSTOMER_DATA,
  TOGGLE_ROAMING_POPUP
} from '@/store/modules/productmanagement/constants/store.constants'
import PwPopup from '@/components/modal/PopUp.vue'

const props = defineProps({
  itemsByProps: {
    type: Array,
    default: null
  },
  listClass: {
    type: String,
    default: 'h-full'
  }
})
const isDark = ref(localStorage.theme === 'dark')

const emit = defineEmits<{
  updateSelectedProductsForUnsubscribe: [value: IContractProduct[]]
}>()

const store = useStore()
const { t } = useI18n()

const itemsFromProps: Ref<any> = ref(props.itemsByProps)
const autoSizeStrategy: Ref<any> = ref(null)
const isFullWidthRow: Ref<any> = ref(null)
const fullWidthCellRenderer: Ref<any> = ref(null)
const gridApi = ref()
const getRowHeight: Ref<any> = ref(null)
const columnDefs: Ref<any> = ref([])
const rowData: Ref<IContractProduct[]> = ref([])
const rowSelection: Ref<{
  mode: string
  hideDisabledCheckboxes: boolean
  isRowSelectable: (node: RowSelectedEvent<IContractProduct>) => boolean
} | null> = ref(null)
const rowClassRules: Ref<any> = ref(null)
const selectedRow: Ref<IContractProduct[]> = ref([])

const isLightTheme = localStorage.getItem('theme') === 'light'
const style = isLightTheme
  ? `ag-theme-quartz ${props.listClass}`
  : `ag-theme-quartz-dark ${props.listClass}`

const defaultColDef = ref({
  sortable: true,
  wrapText: true,
  autoHeight: true,
  cellStyle: {
    wordBreak: 'normal',
    lineHeight: '1.6'
  }
})

const contractProducts: ComputedRef<any> = computed(
  () => store.getters[`${PRODUCTMANAGEMENT}/${GET_CONTRACT_PRODUCTS}`]
)

const customerData: ComputedRef<ICustomer> = computed(
  () => store.getters[`${PRODUCTMANAGEMENT}/${GET_CUSTOMER_DATA}`]
)

const popupRoamingVisible = computed(() => store.state[PRODUCTMANAGEMENT].showRoamingPopup)

watch(
  () => contractProducts.value,
  () => {
    rowData.value = getAllProducts()
  },
  { deep: true }
)

onBeforeMount(async () => {
  columnDefs.value = columnsFieldOptions.map((col) => ({
    ...col,
    headerName: t(col.headerName)
  }))

  autoSizeStrategy.value = { type: 'fitGridWidth' }

  getRowHeight.value = (params: any) => {
    if (isFullWidth(params.data)) return 500
  }

  rowData.value = getAllProducts()

  isFullWidthRow.value = (params: any) => isFullWidth(params.rowNode.data)

  rowSelection.value = {
    mode: 'multiRow',
    hideDisabledCheckboxes: true,
    isRowSelectable: (node: RowSelectedEvent<IContractProduct>) =>
      node.data ? node.data.state.toLowerCase() === 'active' : false
  }
})

const closePopup = () => {
  store.dispatch(`${PRODUCTMANAGEMENT}/${TOGGLE_ROAMING_POPUP}`, false)
}

const confirmRoaming = () => {
  store.dispatch(`${PRODUCTMANAGEMENT}/${TOGGLE_ROAMING_POPUP}`, false)
}

const onGridReady = async (params: any) => {
  gridApi.value = params.api
  window.onresize = () => gridApi.value.sizeColumnsToFit()
  rowData.value = getAllProducts()
}

const getSelectedRows = (event: any) => {
  const selectedNodes = gridApi.value.getSelectedNodes()
  const selectedProducts = selectedNodes.map((node: any) => node.data)
  selectedRow.value = selectedProducts
  emit('updateSelectedProductsForUnsubscribe', selectedProducts)
}

const getAllProducts = () => {
  const productsRowData: any[] = []
  if (!contractProducts.value) return []

  contractProducts.value.forEach((element: any) => {
    const newItem: any = {}
    getNewRows(element, newItem)
    productsRowData.push(newItem)
  })
  return productsRowData
}

const getNewRows = (item: any, newItem: any) => {
  columnDefs.value.forEach((col: any) => {
    newItem[col.field] = item[col.field] === null ? '-' : item[col.field]
  })
}

const isFullWidth = (data: any) => data.isChild
</script>
