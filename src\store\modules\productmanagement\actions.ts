import type { ActionContext } from 'vuex'
import apiCrmService from '@/services/api/crm/apiCrmService'
import {
  SET_LANG,
  SET_USERNAME,
  SET_COMPANY,
  SET_USER_INFO,
  UPDATE_ACCESS_TOKEN,
  UPDATE_ACCOUNT,
  SET_AUTHENTICATION_ERROR,
  SET_IS_LOADING,
  GET_CONTRACT_PRODUCTS,
  DEACTIVATE_CONTRACT_PRODUCT_BY_ID,
  SET_FILTER_BY_TYPE,
  GET_PICKLISTS,
  GET_PICKLISTS_CRM,
  LOAD_MORE_CONTRACT_PRODUCTS,
  SET_FILTER_BY_STATUS,
  SET_DOCUMENT_NUMBER,
  SET_USER_COMPANY,
  UPDATE_ROAMING_STATUS,
  SET_MSAL_INSTANCE,
  SET_PRODUCT_TO_TOGGLE_ROAMING,
  SET_SHOW_ROAMING_POPUP
} from './constants/store.constants'
import type { IState } from '@/store/modules/productmanagement/interfaces/state.interface'
import type { IUserInfo } from '@/services/api/shoppingcart/interfaces/user-info.interface'
import type { IAccount } from './interfaces/account.interface'
import type { IContractProductsRequestBody } from '@/services/api/crm/interfaces/product-data-api-params.interface'
import apiProvisioningService from '@/services/api/provisioning/apiProvisioningService'
import apiShoppingcartService from '@/services/api/shoppingcart/apiShoppingService'
import type { IPickList } from '@/services/api/provisioning/interfaces/picklist.interface'
import type { IAllContractProducts } from '@/services/api/crm/interfaces/contract-products.interface'
import type { IRoamingParams } from '@/services/api/provisioning/interfaces/roaming-status-api-params.interface'
import type { IUpdateRoamingStatus } from '@/services/api/provisioning/interfaces/update-roaming-status.interface'
import type { IApiRes } from '@/services/api/interfaces'
import type { IDeleteCustomerParams } from '@/services/api/crm/interfaces/delete-customer-params.interface'
import type { IDeactivateProductParams } from '@/services/api/crm/interfaces/deactivate-product-params.interface'
import type { IPublicClientApplication } from '@azure/msal-browser'
// import type { IUnsubscribeCustomerByIdParams } from '@/services/api/crm/interfaces/unsubscribe-product-params.interface'

export const setLang = (context: ActionContext<IState, IState>, lang: string): void => {
  context.commit(SET_LANG, lang)
}

export const setMsalInstance = (
  context: ActionContext<IState, IState>,
  msalInstance: IPublicClientApplication
): void => {
  context.commit(SET_MSAL_INSTANCE, msalInstance)
}

export const getUserInfo = async (
  context: ActionContext<IState, IState>,
  username: string
): Promise<IUserInfo> => {
  const userInfo: IUserInfo = await apiShoppingcartService.getUserInfo(username)
  context.dispatch(SET_USER_INFO, userInfo)
  return userInfo
}

export const setUserInfo = (context: ActionContext<IState, IState>, userInfo: IUserInfo): void => {
  context.commit(SET_USER_INFO, userInfo)
  if (userInfo.username) {
    context.commit(SET_USERNAME, userInfo.username)
    userInfo.company && context.commit(SET_USER_COMPANY, userInfo.company[0])
  }
}

export const updateAccessToken = (context: ActionContext<IState, IState>, accessToken: string) => {
  context.commit(UPDATE_ACCESS_TOKEN, accessToken)
}

export const updateAccount = (context: ActionContext<IState, IState>, account: IAccount) => {
  context.commit(UPDATE_ACCOUNT, account)
}

export const setAuthenticationError = (
  context: ActionContext<IState, IState>,
  error: boolean
): void => {
  context.commit(SET_AUTHENTICATION_ERROR, error)
}

/* export const setAccessToken = (
  context: ActionContext<IState, IState>,
  accessToken: string
): void => {
  context.commit(SET_ACCESS_TOKEN, accessToken)
}

export const setAccount = (context: ActionContext<IState, IState>, account: IAccount): void => {
  context.commit(SET_ACCOUNT, account)
}
 */
export const setCompany = (context: ActionContext<IState, IState>, company: string): void => {
  context.commit(SET_COMPANY, company)
}

export const setIsLoading = (context: ActionContext<IState, IState>, isLoading: boolean): void => {
  context.commit(SET_IS_LOADING, isLoading)
}

export const setDocumentNumber = (
  context: ActionContext<IState, IState>,
  documentNumber: string
): void => {
  context.commit(SET_DOCUMENT_NUMBER, documentNumber)
}

export const getContractProducts = async (
  context: ActionContext<IState, IState>,
  { requestBody, document }: { requestBody: IContractProductsRequestBody; document: string }
): Promise<void> => {
  const contractProducts: IAllContractProducts = await apiCrmService.getContractProducts(
    requestBody,
    document
  )

  if (requestBody.pageNumber === 1) {
    context.commit(GET_CONTRACT_PRODUCTS, contractProducts)
  } else {
    context.commit(LOAD_MORE_CONTRACT_PRODUCTS, contractProducts.items)
  }
}

export const getPicklists = async (
  context: ActionContext<IState, IState>,
  language: string
): Promise<void> => {
  const picklists: IPickList[] = await apiProvisioningService.getPicklists(language)
  context.commit(GET_PICKLISTS, picklists)
}

export const getPicklistsCRM = async (
  context: ActionContext<IState, IState>,
  language: string
): Promise<void> => {
  const picklistsCRM: IPickList[] = await apiCrmService.getPicklists(language)
  context.commit(GET_PICKLISTS_CRM, picklistsCRM)
}

export const deactivateContractProductById = async (
  context: ActionContext<IState, IState>,
  deactivateProductParams: IDeactivateProductParams
): Promise<number> => {
  const status: number = await apiCrmService.deactivateContractProductById(deactivateProductParams)
  return status
}

export const deactivateContractProductList = async (
  context: ActionContext<IState, IState>,
  deactivateProductParams: IDeactivateProductParams
): Promise<number> => {
  const status: number = await apiCrmService.deactivateContractProducts(deactivateProductParams)
  return status
}

export const deleteCustomerByDocumentId = async (
  context: ActionContext<IState, IState>,
  deleteCustomerParams: IDeleteCustomerParams
): Promise<number> => {
  const status: number = await apiCrmService.deleteCustomerByDocumentId(deleteCustomerParams)
  return status
}

export const setFilterByType = async (
  context: ActionContext<IState, IState>,
  provisioningClass: string
): Promise<void> => {
  context.commit(SET_FILTER_BY_TYPE, provisioningClass)
}

export const setFilterByStatus = async (
  context: ActionContext<IState, IState>,
  statusProduct: string
): Promise<void> => {
  context.commit(SET_FILTER_BY_STATUS, statusProduct)
}

export const syncCustomerDataFromGossan = async (
  context: ActionContext<IState, IState>,
  requestBody: { company: string; document: string }
): Promise<number> => {
  const response: IApiRes<void> = await apiCrmService.syncCustomerDataFromGossan(requestBody)
  return response.status
}

export const getInitRoamingStatus = async (
  context: ActionContext<IState, IState>,
  payload: IRoamingParams
): Promise<any> => {
  const getRoamingStatus: IUpdateRoamingStatus = await apiProvisioningService.getInitRoamingStatus(
    payload
  )
  return getRoamingStatus
}

export const updateRoamingStatus = async (
  context: ActionContext<IState, IState>,
  payload: IRoamingParams
): Promise<any> => {
  const updateRoamingStatus: IUpdateRoamingStatus =
    await apiProvisioningService.updateRoamingStatus(payload)

  return updateRoamingStatus
}

export const toggleRoamingPopup = (
  context: ActionContext<IState, IState>,
  { visible, product }: { visible: boolean; product?: any }
): void => {
  context.commit(SET_PRODUCT_TO_TOGGLE_ROAMING, visible ? product : null)
  context.commit(SET_SHOW_ROAMING_POPUP, visible)
}

export const syncCustomerFromGossan = async (
  context: ActionContext<IState, IState>,
  query: { documentNumber: string; user: string; persistCustomerSyncChanges: boolean }
): Promise<number> => {
  const response: IApiRes<void> = await apiProvisioningService.syncCustomerFromGossan(query)
  return response.status
}
