import type { IContractProduct } from '@/services/api/crm/interfaces/contract-products.interface'
import type { IContractProductsRequestBody } from '@/services/api/crm/interfaces/product-data-api-params.interface'
import type { IUserInfo } from '@/services/api/shoppingcart/interfaces/user-info.interface'
import type { IPickList } from '@/services/api/provisioning/interfaces/picklist.interface'
import type { IAccount } from '@/store/modules/productmanagement/interfaces/account.interface'
import type { ICustomer } from './customer.interface'
import type { IUpdateRoamingStatus } from '@/services/api/provisioning/interfaces/update-roaming-status.interface'
import type { IPublicClientApplication } from '@azure/msal-browser'

export interface IState {
  lang: string
  userInfo: IUserInfo | null
  username: string
  company: string
  userCompany: string | null
  msalInstance?: IPublicClientApplication
  accessToken: string
  account: IAccount | undefined
  documentNumber: string | undefined
  authenticationError: boolean
  contractProducts: IContractProduct[] | null
  totalProducts: number
  pageNumber: number
  customerData: ICustomer | null
  picklists: IPickList[] | null
  picklistsCRM: IPickList[] | null | any
  isLoading: boolean
  filterTypeByProduct: string | null | undefined
  filterByStatus: string | null | undefined
  initSearchParams: IContractProductsRequestBody
  roamingStatus: boolean | null | IUpdateRoamingStatus
  showRoamingPopup: false,
  productToToggleRoaming: null
}
