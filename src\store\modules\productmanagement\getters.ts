import type { IContractProduct } from '@/services/api/crm/interfaces/contract-products.interface'
import type { IState } from './interfaces/state.interface'
import type { IPickListValue } from '@/services/api/provisioning/interfaces/picklist.interface'
import type { IContractProductsRequestBody } from '@/services/api/crm/interfaces/product-data-api-params.interface'
import type { ICustomer } from './interfaces/customer.interface'
import type { IUpdateRoamingStatus } from '@/services/api/provisioning/interfaces/update-roaming-status.interface'
import type { IPublicClientApplication } from '@azure/msal-browser'

export const getMsalInstance = (state: IState): IPublicClientApplication | undefined => {
  return state.msalInstance
}

export const getAccountUsername = (state: IState): string | undefined => {
  return state.account?.username
}

export const getLang = (state: IState): string => {
  return state.lang
}

export const getUsername = (state: IState): string => {
  return state.username
}

export const getCompany = (state: IState): string | null => {
  return state.company
}

export const getAuthenticationError = (state: IState): boolean => {
  return state.authenticationError
}

export const getDocumentNumber = (state: IState): string | undefined => {
  return state.documentNumber
}

export const getContractProducts = (state: IState): IContractProduct[] | null => {
  return state.contractProducts
}

export const getCustomerData = (state: IState): ICustomer | null => {
  return state.customerData
}

export const getTotalProducts = (state: IState): number => {
  return state.totalProducts
}

export const getProvisioningClass = (state: IState): IPickListValue[] | undefined => {
  return state.picklists?.find((item: any) => item.name === 'ProvisiongClass')?.values
}

export const getPicklistsCRM = (state: IState): IPickListValue[] | undefined => {
  return state.picklistsCRM
}

export const getIsLoading = (state: IState): boolean => {
  return state.isLoading
}

export const getFilterByType = (state: IState): string | null | undefined => {
  return state.filterTypeByProduct
}

export const getFilterByStatus = (state: IState): string | null | undefined => {
  return state.filterByStatus
}

export const getInitSearchParams = (state: IState): IContractProductsRequestBody => {
  return state.initSearchParams
}

export const getRoamingStatus = (state: IState): boolean | null | IUpdateRoamingStatus => {
  return state.roamingStatus
}
