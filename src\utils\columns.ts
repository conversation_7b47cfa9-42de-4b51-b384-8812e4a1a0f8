import DateSellRenderer from '@/components/list/cell-renderers/DateSellRenderer.vue'
import StateCellRenderer from '@/components/list/cell-renderers/StateCellRenderer.vue'
import ActionsCellRenderer from '@/components/list/cell-renderers/ActionsCellRenderer.vue'
import type { IColumn } from '@/components/list/interfaces/column.interface.ts'
import TypeCellRenderer from '@/components/list/cell-renderers/TypeCellRenderer.vue'
import TerminalCellRenderer from '@/components/list/cell-renderers/TerminalCellRenderer.vue'

const columnsFieldOptions: IColumn[] = [
  {
    field: 'id',
    headerName: 'columns.contractProductId',
    width: 100,
    hide: true
  },
  {
    field: 'productName',
    headerName: 'columns.productName',
    width: 150,
    minWidth: 80
  },
  {
    field: 'provisioningClass',
    headerName: 'columns.type',
    width: 60,
    cellRenderer: TypeCellRenderer
  },
  {
    field: 'number',
    headerName: 'columns.terminal',
    width: 90,
    cellRenderer: TerminalCellRenderer
  },
  {
    field: 'terminalSerialNumber',
    headerName: 'columns.terminal',
    hide: true
  },
  {
    field: 'state',
    headerName: 'columns.state',
    cellRenderer: StateCellRenderer,
    width: 130,
    maxWidth: 150,
    minWidth: 130
  },
  {
    field: 'activationDate',
    headerName: 'columns.activationDate',
    cellRenderer: DateSellRenderer,
    width: 80,
    sortable: true
  },
  {
    field: 'soldAt',
    headerName: 'columns.soldAt',
    cellRenderer: DateSellRenderer,
    width: 80,
    sortable: true
  },
  {
    field: 'effectiveEndDate',
    headerName: 'columns.effectiveEndDate',
    cellRenderer: DateSellRenderer,
    width: 80
  },
  {
    field: 'unsubscribeReason',
    headerName: 'columns.unsubscribeReason',
    width: 100
  },
  {
    field: 'contractProductId',
    headerName: 'columns.actions',
    cellRenderer: ActionsCellRenderer,
    width: 100
  },
  {
    field: 'customerId',
    headerName: 'columns.customerId',
    hide: true
  },
  {
    field: 'provider',
    headerName: 'columns.provider',
    hide: true
  },
  {
    field: 'externalSubscriptionId',
    headerName: 'columns.externalSubscriptionId',
    hide: true
  },
  {
    field: 'contractedSubscriptionId',
    headerName: 'columns.contractedSubscriptionId',
    hide: true
  },
  {
    field: 'provisioningClass',
    headerName: 'columns.provisioningClass',
    hide: true
  },
  {
    field: 'provisioningSubClass',
    headerName: 'columns.provisioningSubClass',
    hide: true
  }
]

export default columnsFieldOptions
