import type { IState } from './interfaces/state.interface'

export default (): IState => ({
  lang: 'ca',
  userInfo: null,
  company: '<PERSON>rle<PERSON>',
  userCompany: null,
  username: 'nouBackEnd',
  msalInstance: undefined,
  accessToken: '',
  account: undefined,
  authenticationError: false,
  documentNumber: undefined,
  contractProducts: null,
  totalProducts: 0,
  pageNumber: 1,
  customerData: null,
  picklists: null,
  picklistsCRM: null,
  isLoading: false,
  filterTypeByProduct: undefined,
  filterByStatus: undefined,
  initSearchParams: {
    pageNumber: 1,
    pageSize: 20,
    state: 'Active'
  },
  roamingStatus: null,
  showRoamingPopup: false,
  productToToggleRoaming: null,
  customerSyncStatus: null
})
