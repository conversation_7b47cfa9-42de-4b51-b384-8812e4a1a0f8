<template>
  <span>{{ getTerminalValue() }}</span>
</template>

<script setup lang="ts">
const props = defineProps<{
  params: {
    data: any
  }
}>()

const getTerminalValue = () => {
  const data = props.params.data
  console.log(data)
  // Si provisioningClass es "TV", mostrar terminal.serialNumber
  if (data.provisioningClass === 'TV') {
    return data.terminalSerialNumber
  }
  // En caso contrario, mostrar el number (MSISDN) o '-' si no existe
  return data.number || '-'
}
</script>
