{"name": "parlem-webcomponents-productmanagement", "version": "1.0.23", "description": "PWC Productmanagement", "homepage": "https://<EMAIL>/ParlemTelecom/Parlem%20WebComponents/_git/parlem-webcomponents-productmanagement", "main": "./dist/parlem-webcomponents-productmanagement.umd.js", "module": "./dist/parlem-webcomponents-productmanagement.es.js", "exports": {".": {"import": "./dist/parlem-webcomponents-productmanagement.es.js", "require": "./dist/parlem-webcomponents-productmanagement.umd.js"}}, "files": ["dist/*"], "scripts": {"dev": "vite --force true", "staging": "vite --mode staging --force true", "build": "run-p type-check build:prod", "preview": "vite preview", "test:unit": "vitest", "build:dev": "vite build --mode dev", "build:staging": "vite build --mode staging", "build:prod": "vite build", "type-check": "vue-tsc --noEmit -p tsconfig.vitest.json --composite false", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "author": {"name": "ParlemTelecom"}, "contributors": [], "repository": {"url": "https://<EMAIL>/ParlemTelecom/Parlem%20WebComponents/_git/parlem-webcomponents-productmanagement", "type": "git"}, "bugs": {"url": "https://<EMAIL>/ParlemTelecom/Parlem%20WebComponents/_git/parlem-webcomponents-productmanagement"}, "dependencies": {"@azure/msal-browser": "^4.13.1", "@ag-grid-community/styles": "^32.2.2", "@fortawesome/fontawesome-svg-core": "^6.4.0", "@fortawesome/free-brands-svg-icons": "^6.4.0", "@fortawesome/free-regular-svg-icons": "^6.4.0", "@fortawesome/free-solid-svg-icons": "^6.4.0", "@fortawesome/vue-fontawesome": "^3.0.3", "@intlify/unplugin-vue-i18n": "^0.10.0", "@vuelidate/core": "^2.0.2", "@vuelidate/validators": "^2.0.2", "ag-grid-community": "^32.3.0", "ag-grid-vue3": "^32.3.0", "axios": "^1.5.1", "moment": "^2.29.4", "parlem-webcomponents-common": "^1.1.226", "postcss-nesting": "^11.2.2", "vue": "^3.5.12", "vue-i18n": "^9.2.2", "vue-router": "^3.0.1", "vue-toast-notification": "^3.1.3", "vuex": "^4.1.0"}, "devDependencies": {"@rushstack/eslint-patch": "^1.2.0", "@types/jsdom": "^21.1.1", "@types/node": "^18.19.31", "@vitejs/plugin-vue": "^4.2.2", "@vue/eslint-config-prettier": "^7.1.0", "@vue/eslint-config-typescript": "^11.0.3", "@vue/test-utils": "^2.3.2", "@vue/tsconfig": "^0.1.3", "autoprefixer": "^10.4.19", "eslint": "^8.40.0", "eslint-plugin-vue": "^9.12.0", "jsdom": "^21.1.2", "npm-run-all": "^4.1.5", "postcss": "^8.4.38", "prettier": "^2.8.8", "tailwindcss": "^3.4.3", "typescript": "~5.4.5", "vite": "^4.3.5", "vitest": "^0.29.8", "vue-tsc": "^1.8.27"}, "peerDependencies": {"postcss": "^8.4.38"}}