import type { ITab } from '@/components/filter/tabs/interfaces/tabs.interface'

export const tabsFilterProductFields: any = [
  { label: 'tabs.product.all', value: '', disabled: false, active: true },
  {
    label: 'tabs.product.mobile',
    value: 'Mobile',
    disabled: false,
    active: true,
    icon: 'fa fa-mobile-alt'
  },
  {
    label: 'tabs.product.fiber',
    value: 'Fiber',
    disabled: false,
    active: true,
    icon: 'fa fa-wifi'
  },
  {
    label: 'tabs.product.landline',
    value: 'LandLine',
    disabled: false,
    active: true,
    icon: 'fa fa-phone'
  }
]

export const tabsFilterStatusFields: ITab[] = [
  { label: 'tabs.status.all', value: '', disabled: false, active: true },
  { label: 'tabs.status.active', value: 'Active', disabled: false, active: true },
  { label: 'tabs.status.pending', value: 'Pending', disabled: false, active: true },
  { label: 'tabs.status.inactive', value: 'Inactive', disabled: false, active: true }
]

export const customerManagerTabs: ITab[] = [
  {
    label: 'tabs.customer.sync',
    value: 'syncro-customer',
    disabled: false,
    active: true,
    icon: 'fa fa-rotate'
  }
  /*   {
    label: 'tabs.customer.delete',
    value: 'delete-customer',
    disabled: false,
    active: true,
    icon: 'fa fa-user-slash'
  } */
]

export const productActionsTabs: ITab[] = [
  {
    label: 'tabs.product.sync',
    value: 'sync-products',
    disabled: false,
    active: true,
    icon: 'fa fa-rotate'
  },
  {
    label: 'tabs.product.unsubscribe',
    value: 'delete-products',
    disabled: false,
    active: true,
    icon: 'fa fa-trash-can'
  }
]
