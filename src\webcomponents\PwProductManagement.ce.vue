<template>
  <PwAuthenticationError v-if="authenticationError" :company="company"></PwAuthenticationError>

  <div v-else>
    <div v-if="isLoading" class="flex justify-center mt-10">
      <PwLoading
        :company="company"
        loading-style="w-32 h-32"
        loading-image-style="w-[20px]"
      ></PwLoading>
    </div>
    <div v-else>
      <div v-if="customerDocumentNumber" >
        <customerManagement v-if="!hidePersonalData"></customerManagement>
        <PwFilter :lang="lang" :document-number="documentNumber" ></PwFilter>
      </div>
      <searchCustomer v-else></searchCustomer>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
import { createScript } from '@/utils'
export default defineComponent({
  name: 'PwProductManagement'
})
</script>

<script setup lang="ts">
import PwFilter from '@/components/filter/PwFilter.vue'
import { useStore } from 'vuex'
import {
  SET_LANG,
  PRODUCTMANAGEMENT,
  GET_USER_INFO,
  SET_USER_INFO,
  GET_ACCOUNT_USERNAME,
  GET_AUTHENTICATION_ERROR,
  SET_COMPANY,
  GET_PICKLISTS,
  GET_PICKLISTS_CRM,
  GET_LANG,
  GET_DOCUMENT_NUMBER,
  SET_DOCUMENT_NUMBER,
  GET_USER_COMPANY
} from '@/store/modules/productmanagement/constants/store.constants'

import { useI18n } from 'vue-i18n'
import checkUserCredentials from '@/utils/userCredential'
import { changeColors, PwLoading, PwAuthenticationError } from 'parlem-webcomponents-common'
import { computed, onBeforeMount, ref } from 'vue'
import type { ComputedRef, Ref } from 'vue'
import customerManagement from '@/components/customer-management/customerManagement.vue'
import searchCustomer from '@/components/search-customer/SearchCustomer.vue'

const props = defineProps({
  lang: {
    type: String,
    default: 'ca'
  },
  userInfo: {
    type: String,
    default: null
  },
  company: {
    type: String,
    default: null
  },
  documentNumber: {
    type: String,
    default: null
  },
  hidePersonalData: {
    type: Boolean,
    default: null
  }
})

const store = useStore()
const { locale }: any = useI18n()
const isLoading: Ref<boolean> = ref(false)
const accountUsername: ComputedRef<boolean> = computed(
  () => store.getters[`${PRODUCTMANAGEMENT}/${GET_ACCOUNT_USERNAME}`]
)
const userCompany: ComputedRef<string> = computed(
  () => store.getters[`${PRODUCTMANAGEMENT}/${GET_USER_COMPANY}`]
)
const authenticationError: ComputedRef<string> = computed(
  () => store.getters[`${PRODUCTMANAGEMENT}/${GET_AUTHENTICATION_ERROR}`]
)
const language = computed(() => store.getters[`${PRODUCTMANAGEMENT}/${GET_LANG}`])
const customerDocumentNumber: ComputedRef<string> = computed(
  () => store.getters[`${PRODUCTMANAGEMENT}/${GET_DOCUMENT_NUMBER}`]
)

//GET PICKLIST
const getPicklists = async () => {
  await store.dispatch(`${PRODUCTMANAGEMENT}/${GET_PICKLISTS}`, language.value)
}

const getPicklistsCRM = async () => {
  await store.dispatch(`${PRODUCTMANAGEMENT}/${GET_PICKLISTS_CRM}`, language.value)
}

onBeforeMount(async () => {
  isLoading.value = true
  if (props.company) {
    changeColors(props.company)
    store.dispatch(`${PRODUCTMANAGEMENT}/${SET_COMPANY}`, props.company)
  }
  await checkUserCredentials()
  if (accountUsername.value) {
    if (props.userInfo) {
      store.dispatch(`${PRODUCTMANAGEMENT}/${SET_USER_INFO}`, JSON.parse(props.userInfo))
    } else {
      await store.dispatch(`${PRODUCTMANAGEMENT}/${GET_USER_INFO}`, accountUsername.value)
    }
  }
  changeLanguage(props.lang)
  changeColors(props.company ? props.company : userCompany.value ? userCompany.value : 'Parlem')
  getPicklists()
  getPicklistsCRM()
  if (props.documentNumber) {
    store.dispatch(`${PRODUCTMANAGEMENT}/${SET_DOCUMENT_NUMBER}`, props.documentNumber)
  }

  createScript('VITE_COVERAGE_WC', 'coverage')

  isLoading.value = false
})

function changeLanguage(lang: string) {
  locale.value = props.lang
  store.dispatch(`${PRODUCTMANAGEMENT}/${SET_LANG}`, lang)
}
</script>

<style>
@import '@/assets/styles/index';
@import '~/parlem-webcomponents-common/dist/parlem-webcomponents-common.css';
</style>
