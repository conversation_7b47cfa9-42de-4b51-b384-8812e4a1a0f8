import type { IPickList } from './picklist.interface'
import type { IRoamingParams } from './roaming-status-api-params.interface'
import type { IUpdateRoamingStatus } from './update-roaming-status.interface'
// import type { IUserInfo } from './user-info.interface'

export interface IApiProvisioning {
  getPicklists(language: string): Promise<IPickList[]>
  getInitRoamingStatus(payload: IRoamingParams): Promise<IUpdateRoamingStatus>
  updateRoamingStatus(payload: IRoamingParams): Promise<IUpdateRoamingStatus>
  syncCustomerFromGossan(query: {
    documentNumber: string
    user: string
    persistCustomerSyncChanges: boolean
  }): Promise<any>
}
