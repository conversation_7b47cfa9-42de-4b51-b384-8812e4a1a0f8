<template>
  <main
    class="bg-white dark:bg-dark-gray-light w-full rounded-md shadow-[0_3px_10px_-1px_rgb(0,0,0,0.15)] rounded"
  >
    <div class="flex justify-between items-center py-3 px-4 border-b border-gray-light">
      <div class="flex items-center gap-4">
        <div class="rounded-full size-10 bg-primary-light flex justify-center items-center mr-2">
          <font-awesome-icon icon="fa fa-hippo" class="size-6 text-primary" />
        </div>
        <div class="text-black dark:text-white">
          <h2 class="font-light text-sm">{{ $t('products.title') }}</h2>
          <p class="-mt-1 text-lg font-bold">{{ $t('products.contractedProducts') }}</p>
        </div>
      </div>
      <div>
        <PwManage
          :updateSelectedProductForUnsubscribe="selectedProductsActiveForUnsubscribe"
          @refresh-table="refreshTableUnsubscribeProduct"
        ></PwManage>
      </div>
    </div>
    <div class="px-4 pb-4 pt-4">
      <div class="w-full">
        <div class="flex w-full justify-between items-center mb-4">
          <PwTabsComponent></PwTabsComponent>
        </div>

        <PwFilter
          :lang="lang"
          :first-filter-options="firstFilterOptions"
          @submitFilter="filter"
          @filterError="filter"
          :filters="[]"
          class="w-full"
          :class="{ dark: isDark }"
        ></PwFilter>
        <div
          class="mt-4"
          :class="hasFilters ? 'h-[calc(100vh-460px-60px)]' : 'h-[calc(100vh-460px)]'"
        >
          <div v-if="isLoadingContain" class="flex justify-center mt-10">
            <PwLoading
              :company="company"
              loading-style="w-32 h-32"
              loading-image-style="w-[20px]"
            ></PwLoading>
          </div>
          <ProductManagementList
            v-else
            @update-selected-products-for-unsubscribe="updateSelectedProductsForUnsubscribe"
          ></ProductManagementList>
        </div>

        <PwPagination
          @get-more="getMoreItems"
          :is-loading-more-items="isLoadingMoreItems"
          :request-filter-body="searchParams"
        ></PwPagination>
      </div>
    </div>
  </main>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
import {
  GET_CONTRACT_PRODUCTS,
  GET_COMPANY,
  PRODUCTMANAGEMENT,
  GET_IS_LOADING,
  SET_IS_LOADING,
  GET_FILTER_BY_TYPE,
  GET_INIT_SEARCH_PARAMS,
  GET_FILTER_BY_STATUS,
  GET_DOCUMENT_NUMBER
} from '@/store/modules/productmanagement/constants/store.constants'
import type { IFirstFilter } from 'parlem-webcomponents-common/dist/components/pw-filter/interfaces/firstFilter.interface'
import type { IContractProductsRequestBody } from '@/services/api/crm/interfaces/product-data-api-params.interface'

export default defineComponent({
  name: 'PwFilter'
})
</script>

<script setup lang="ts">
import { computed, onMounted, ref, watch, type ComputedRef, type Ref } from 'vue'
import PwTabsComponent from './tabs/PwTabsComponent.vue'
import PwManage from '../manage/PwManage.vue'
import ProductManagementList from '../list/ProductManagementList.vue'
import PwPagination from '../pagination/PwPagination.vue'
import { PwFilter, PwLoading } from 'parlem-webcomponents-common'
import {
  customerSearchFilters,
  filterByMobilType,
  filterByFiberType,
  filterByFixType
} from '@/utils'
import { useStore } from 'vuex'
import type { IContractProduct } from '@/services/api/crm/interfaces/contract-products.interface'
import { toastUtils } from '@/utils/toast'

const props = defineProps({
  lang: {
    type: String,
    default: null
  }
})

const isDark = ref(localStorage.theme === 'dark')

const store = useStore()
const selectedProductsActiveForUnsubscribe: Ref<IContractProduct[]> = ref([])
const company: ComputedRef<string> = computed(
  () => store.getters[`${PRODUCTMANAGEMENT}/${GET_COMPANY}`]
)
const initSearchParams: ComputedRef<IContractProductsRequestBody> = computed(
  () => store.getters[`${PRODUCTMANAGEMENT}/${GET_INIT_SEARCH_PARAMS}`]
)
const documentNumber: ComputedRef<string> = computed(
  () => store.getters[`${PRODUCTMANAGEMENT}/${GET_DOCUMENT_NUMBER}`]
)
const searchParams: Ref<IContractProductsRequestBody> = ref({ ...initSearchParams })
const isLoadingMoreItems: Ref<boolean> = ref(false)
const filterType = computed(() => store.getters[`${PRODUCTMANAGEMENT}/${GET_FILTER_BY_TYPE}`])
const filterStatus = computed(() => store.getters[`${PRODUCTMANAGEMENT}/${GET_FILTER_BY_STATUS}`])
const firstFilterOptions: Ref<IFirstFilter[]> = computed(() => {
  return customerSearchFilters
})

const isLoadingContain = computed(() => store.getters[`${PRODUCTMANAGEMENT}/${GET_IS_LOADING}`])
const hasFilters: Ref<boolean> = ref(false)

onMounted(async () => {
  await filter()
})

const getProductsByFilter = async (requestFilterParams: any): Promise<void> => {
  const payload = {
    requestBody: requestFilterParams,
    document: documentNumber.value
  }

  await store.dispatch(`${PRODUCTMANAGEMENT}/${GET_CONTRACT_PRODUCTS}`, payload)
  store.dispatch(`${PRODUCTMANAGEMENT}/${SET_IS_LOADING}`, false)
}

async function filter(filterArray: any = null) {
  if (typeof filterArray === 'string') {
    toastUtils.showToast('warning', filterArray)
    return
  }

  store.dispatch(`${PRODUCTMANAGEMENT}/${SET_IS_LOADING}`, true)
  hasFilters.value = false
  searchParams.value = { ...initSearchParams.value }

  if (filterArray && filterArray.length) {
    hasFilters.value = true
    filterArray.forEach((filter: any) => {
      searchParams.value[filter.firstFilter] = filter.secondFilter
    })
  }

  if (filterStatus.value) {
    searchParams.value.state = filterStatus.value
  }

  if (filterType.value) {
    searchParams.value.provisioningClass = filterType.value
  }

  await getProductsByFilter(searchParams.value)
}


const getMoreItems = async (): Promise<void> => {
  isLoadingMoreItems.value = true
  ++searchParams.value.pageNumber
  await getProductsByFilter(searchParams.value)
  isLoadingMoreItems.value = false
}

watch(
  () => [filterStatus.value, filterType.value],
  async () => {
    await filter()
  }
)

const updateSelectedProductsForUnsubscribe = (productsForUnsubscribe: IContractProduct[]) => {
  selectedProductsActiveForUnsubscribe.value = [...productsForUnsubscribe]
}

const refreshTableUnsubscribeProduct = async () => {
  await filter()
  selectedProductsActiveForUnsubscribe.value = []
}
</script>
