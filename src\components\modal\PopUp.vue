<template>
  <PwPopup v-if="isVisible" @close="handleClose" @cancel="handleClose" @accept="handleAccept">
    <div class="px-4 py-12 max-w-[500px] mx-auto text-center flex flex-col items-center gap-4">
      <font-awesome-icon
        :icon="product?.roamingIsActive ? 'fa-solid fa-ban' : 'fa-solid fa-earth-america'"
        class="w-20 h-20"
        :class="product?.roamingIsActive ? 'text-red-500' : 'text-primary'"
      />
      <p class="text-2xl font-medium mt-4 text-black dark:text-white">
        {{ $t('roaming.confirmationQuestion') }}
        <strong>{{
          product?.roamingIsActive ? $t('roaming.deactivate') : $t('roaming.activate')
        }}</strong>
        {{ $t('roaming.confirmationEnd') }}
      </p>
      <div class="text-black dark:text-white">
        <div>Terminal: {{ product.number || '' }}</div>
      </div>
    </div>
  </PwPopup>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'
import { PwPopup } from 'parlem-webcomponents-common'
import {
  PRODUCTMANAGEMENT,
  GET_COMPANY,
  UPDATE_ROAMING_STATUS,
  TOGGLE_ROAMING_POPUP
} from '@/store/modules/productmanagement/constants/store.constants'
import { toastUtils } from '@/utils/toast'

const store = useStore()
const { t } = useI18n()

const isVisible = computed(() => store.state[PRODUCTMANAGEMENT].showRoamingPopup)
const product = computed(() => store.state[PRODUCTMANAGEMENT].productToToggleRoaming)
const handleClose = () => {
  store.dispatch(`${PRODUCTMANAGEMENT}/${TOGGLE_ROAMING_POPUP}`, {
    visible: false,
    product: null
  })
}

const handleAccept = async () => {
  if (!product.value) return

  const company = store.getters[`${PRODUCTMANAGEMENT}/${GET_COMPANY}`]

  const payload = {
    company,
    customerId: product.value.customerId,
    provider: product.value.provider,
    subscriptionId: product.value.contractedSubscriptionId,
    status: !product.value.roamingIsActive
  }

  try {
    const response = await store.dispatch(`${PRODUCTMANAGEMENT}/${UPDATE_ROAMING_STATUS}`, payload)
    if (response?.status === 200 || response?.status === 204) {
      const messageKey = payload.status ? 'activated' : 'deactivated'
      toastUtils.showToast('success', t(`roaming.${messageKey}Success`))
    } else {
      toastUtils.showToast('error', t('roaming.error'))
    }
  } catch (err) {
    toastUtils.showToast('error', t('roaming.unexpectedError'))
  } finally {
    handleClose()
  }
}
</script>
